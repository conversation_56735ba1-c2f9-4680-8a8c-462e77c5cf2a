<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Request Logs - Request Modifier Pro</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="card">
      <div class="card-header">
        <h1 class="card-title">Request Logs</h1>
        <div class="card-subtitle">Complete history of request modifications</div>
      </div>
      <div class="card-body">
        <div class="flex flex-between align-center mb-2">
          <div>
            <span id="totalLogs" class="status status-active">0 total logs</span>
            <span id="todayLogs" class="status status-inactive ml-2">0 today</span>
          </div>
          <div class="flex gap-1">
            <button class="btn btn-outline btn-small" id="exportLogsBtn">
              <span>📤</span> Export
            </button>
            <button class="btn btn-warning btn-small" id="clearAllLogsBtn">
              <span>🗑️</span> Clear All
            </button>
            <button class="btn btn-primary btn-small" id="refreshLogsBtn">
              <span>🔄</span> Refresh
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Filters</h2>
      </div>
      <div class="card-body">
        <div class="flex gap-2 mb-2">
          <div class="form-group flex-1">
            <label class="form-label">Filter by URL</label>
            <input type="text" class="form-input" id="urlFilter" placeholder="Enter URL pattern...">
          </div>
          <div class="form-group">
            <label class="form-label">Type</label>
            <select class="form-select" id="typeFilter">
              <option value="">All Types</option>
              <option value="success">Success</option>
              <option value="error">Error</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">Rule</label>
            <select class="form-select" id="ruleFilter">
              <option value="">All Rules</option>
            </select>
          </div>
        </div>
        <div class="flex gap-2">
          <div class="form-group">
            <label class="form-label">From Date</label>
            <input type="date" class="form-input" id="fromDate">
          </div>
          <div class="form-group">
            <label class="form-label">To Date</label>
            <input type="date" class="form-input" id="toDate">
          </div>
          <div class="form-group align-center">
            <button class="btn btn-secondary" id="applyFiltersBtn" style="margin-top: 24px;">Apply Filters</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Logs List -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Log Entries</h2>
        <div class="card-subtitle">Click on entries for detailed information</div>
      </div>
      <div class="card-body">
        <div id="logsList">
          <div class="text-center" style="color: var(--text-secondary); padding: 40px;">
            Loading logs...
          </div>
        </div>
        
        <!-- Pagination -->
        <div id="pagination" class="flex flex-center gap-1 mt-2" style="display: none;">
          <button class="btn btn-outline btn-small" id="prevPageBtn">Previous</button>
          <span id="pageInfo" class="status status-inactive">Page 1 of 1</span>
          <button class="btn btn-outline btn-small" id="nextPageBtn">Next</button>
        </div>
      </div>
    </div>

    <!-- Log Detail Modal -->
    <div id="logDetailModal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Log Entry Details</h2>
          </div>
          <div class="card-body">
            <div id="logDetailContent">
              <!-- Content will be populated by JavaScript -->
            </div>
            <div class="flex flex-center mt-2">
              <button class="btn btn-outline" id="closeLogDetailBtn">Close</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="utils.js"></script>
  <script src="logs.js"></script>
</body>
</html>
