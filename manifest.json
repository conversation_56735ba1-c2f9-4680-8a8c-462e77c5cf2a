{"manifest_version": 3, "name": "Request Modifier Pro", "version": "1.0.0", "description": "Modern request modifier with intelligent encoding handling and custom response processing", "permissions": ["declarativeNetRequest", "storage", "activeTab", "scripting", "webRequest"], "host_permissions": ["*://*/*"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "Request Modifier Pro"}, "options_page": "options.html", "content_scripts": [{"matches": ["*://*/*"], "js": ["content.js"], "run_at": "document_start"}], "web_accessible_resources": [{"resources": ["utils.js"], "matches": ["*://*/*"]}]}