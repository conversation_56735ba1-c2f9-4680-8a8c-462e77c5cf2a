{"manifest_version": 3, "name": "Request Modifier Pro", "version": "1.0.0", "description": "Modern request modifier with intelligent encoding handling and custom response processing", "permissions": ["declarativeNetRequest", "declarativeNetRequestWithHostAccess", "storage", "activeTab", "scripting", "webRequest", "webRequestBlocking", "<all_urls>"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "Request Modifier Pro"}, "options_page": "options.html", "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start"}], "web_accessible_resources": [{"resources": ["utils.js"], "matches": ["<all_urls>"]}], "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}