// Popup script for Request Modifier Pro

class PopupManager {
  constructor() {
    this.rules = [];
    this.logs = [];
    this.isEnabled = true;
    
    this.init();
  }

  async init() {
    await this.loadData();
    this.setupEventListeners();
    this.updateUI();
  }

  async loadData() {
    try {
      // Get extension status
      const statusResponse = await this.sendMessage({ type: 'getStatus' });
      this.isEnabled = statusResponse.enabled;

      // Get rules
      const rulesResponse = await this.sendMessage({ type: 'getRules' });
      this.rules = rulesResponse.rules || [];

      // Get recent logs
      const logsResponse = await this.sendMessage({ type: 'getLogs' });
      this.logs = logsResponse.logs || [];
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  }

  setupEventListeners() {
    // Enable/disable toggle
    const enableToggle = document.getElementById('enableToggle');
    enableToggle.addEventListener('change', () => this.toggleEnabled());

    // Quick action buttons
    document.getElementById('addRuleBtn').addEventListener('click', () => this.showAddRuleModal());
    document.getElementById('viewLogsBtn').addEventListener('click', () => this.openLogsPage());
    document.getElementById('openOptionsBtn').addEventListener('click', () => this.openOptionsPage());
    document.getElementById('clearLogsBtn').addEventListener('click', () => this.clearLogs());

    // Add rule form
    const addRuleForm = document.getElementById('addRuleForm');
    if (addRuleForm) {
      addRuleForm.addEventListener('submit', (e) => this.handleAddRule(e));
    }

    const cancelAddRule = document.getElementById('cancelAddRule');
    if (cancelAddRule) {
      cancelAddRule.addEventListener('click', () => this.hideAddRuleModal());
    }
  }

  async toggleEnabled() {
    try {
      const response = await this.sendMessage({ type: 'toggleEnabled' });
      this.isEnabled = response.enabled;
      this.updateUI();
    } catch (error) {
      console.error('Failed to toggle enabled state:', error);
    }
  }

  async clearLogs() {
    try {
      await this.sendMessage({ type: 'clearLogs' });
      this.logs = [];
      this.updateUI();
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  }

  showAddRuleModal() {
    // Create modal HTML dynamically
    const modalHTML = `
      <div id="addRuleModal" class="modal">
        <div class="modal-content">
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Add New Rule</h2>
            </div>
            <div class="card-body">
              <form id="addRuleForm">
                <div class="form-group">
                  <label class="form-label">Rule Name</label>
                  <input type="text" class="form-input" id="ruleName" placeholder="e.g., Fix encoding for example.com" required>
                </div>
                
                <div class="form-group">
                  <label class="form-label">URL Pattern</label>
                  <input type="text" class="form-input" id="urlPattern" placeholder="e.g., https://example.com/* or .*\\.example\\.com.*" required>
                  <small style="color: var(--text-secondary);">Use * for wildcards or regex patterns</small>
                </div>
                
                <div class="form-group">
                  <label class="form-label">Rewrite URL (Optional)</label>
                  <input type="text" class="form-input" id="rewriteUrl" placeholder="e.g., https://proxy.example.com/$1">
                  <small style="color: var(--text-secondary);">Use $1, $2, etc. for captured groups</small>
                </div>
                
                <div class="form-group">
                  <label class="form-label">Custom Function (Optional)</label>
                  <textarea class="form-textarea" id="customFunction" placeholder="// Custom JavaScript function to process response
// Parameters: response (object), request (object), utils (object)
// Example:
// if (response.headers['content-type'].includes('text/html')) {
//   console.log('Processing HTML response:', response.url);
//   // Your custom logic here
// }"></textarea>
                </div>
                
                <div class="flex gap-1 mt-2">
                  <button type="submit" class="btn btn-primary flex-1">Save Rule</button>
                  <button type="button" class="btn btn-outline flex-1" id="cancelAddRule">Cancel</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('addRuleModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Setup event listeners for the new modal
    document.getElementById('addRuleForm').addEventListener('submit', (e) => this.handleAddRule(e));
    document.getElementById('cancelAddRule').addEventListener('click', () => this.hideAddRuleModal());

    // Close modal when clicking outside
    document.getElementById('addRuleModal').addEventListener('click', (e) => {
      if (e.target.id === 'addRuleModal') {
        this.hideAddRuleModal();
      }
    });
  }

  hideAddRuleModal() {
    const modal = document.getElementById('addRuleModal');
    if (modal) {
      modal.remove();
    }
  }

  async handleAddRule(event) {
    event.preventDefault();

    const ruleName = document.getElementById('ruleName').value;
    const urlPattern = document.getElementById('urlPattern').value;
    const rewriteUrl = document.getElementById('rewriteUrl').value;
    const customFunction = document.getElementById('customFunction').value;

    // Validate inputs
    if (!ruleName || !urlPattern) {
      alert('Please fill in required fields');
      return;
    }

    // Validate custom function if provided
    if (customFunction.trim()) {
      const validation = Utils.validateCustomFunction(customFunction);
      if (!validation.valid) {
        alert(`Custom function error: ${validation.error}`);
        return;
      }
    }

    const rule = {
      name: ruleName,
      urlPattern: urlPattern,
      rewriteUrl: rewriteUrl || '',
      customFunction: customFunction || '',
      enabled: true,
      createdAt: Date.now()
    };

    try {
      await this.sendMessage({ type: 'saveRule', rule });
      await this.loadData();
      this.updateUI();
      this.hideAddRuleModal();
    } catch (error) {
      console.error('Failed to save rule:', error);
      alert('Failed to save rule');
    }
  }

  openLogsPage() {
    chrome.tabs.create({ url: chrome.runtime.getURL('logs.html') });
  }

  openOptionsPage() {
    chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
  }

  updateUI() {
    this.updateStatus();
    this.updateRulesList();
    this.updateRecentLogs();
  }

  updateStatus() {
    const enableToggle = document.getElementById('enableToggle');
    const statusText = document.getElementById('statusText');
    const ruleCount = document.getElementById('ruleCount');

    enableToggle.checked = this.isEnabled;
    
    if (this.isEnabled) {
      statusText.textContent = 'Active';
      statusText.className = 'status status-active';
    } else {
      statusText.textContent = 'Inactive';
      statusText.className = 'status status-inactive';
    }

    const activeRules = this.rules.filter(rule => rule.enabled).length;
    ruleCount.textContent = `${activeRules} active rules`;
  }

  updateRulesList() {
    const rulesList = document.getElementById('rulesList');
    
    if (this.rules.length === 0) {
      rulesList.innerHTML = `
        <div class="text-center" style="color: var(--text-secondary); padding: 20px;">
          No rules configured yet
        </div>
      `;
      return;
    }

    const recentRules = this.rules.slice(-3).reverse(); // Show last 3 rules
    
    rulesList.innerHTML = recentRules.map(rule => `
      <div class="rule-item">
        <div class="rule-header">
          <div class="rule-name">${Utils.escapeHtml(rule.name)}</div>
          <div class="rule-actions">
            <span class="status ${rule.enabled ? 'status-active' : 'status-inactive'}">
              ${rule.enabled ? 'ON' : 'OFF'}
            </span>
            <button class="btn btn-small btn-outline" onclick="popupManager.toggleRule('${rule.id}')">
              ${rule.enabled ? 'Disable' : 'Enable'}
            </button>
          </div>
        </div>
        <div class="rule-pattern">${Utils.escapeHtml(rule.urlPattern)}</div>
      </div>
    `).join('');
  }

  updateRecentLogs() {
    const recentLogs = document.getElementById('recentLogs');
    
    if (this.logs.length === 0) {
      recentLogs.innerHTML = `
        <div class="text-center" style="color: var(--text-secondary); padding: 20px;">
          No recent activity
        </div>
      `;
      return;
    }

    const recentEntries = this.logs.slice(-5).reverse(); // Show last 5 logs
    
    recentLogs.innerHTML = recentEntries.map(log => `
      <div class="log-entry ${log.type}">
        <div class="log-timestamp">${Utils.formatTimestamp(log.timestamp)}</div>
        <div class="log-url">${Utils.truncateText(log.url, 50)}</div>
        <div class="log-details">
          ${log.details.rule ? `Rule: ${log.details.rule}` : ''}
          ${log.details.type ? ` | ${log.details.type}` : ''}
        </div>
      </div>
    `).join('');
  }

  async toggleRule(ruleId) {
    try {
      await this.sendMessage({ type: 'toggleRule', ruleId });
      await this.loadData();
      this.updateUI();
    } catch (error) {
      console.error('Failed to toggle rule:', error);
    }
  }

  sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }
}

// Initialize popup manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.popupManager = new PopupManager();
});
