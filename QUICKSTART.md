# Request Modifier Pro - 快速开始

## 🚀 5分钟快速上手

### 1. 安装扩展（2分钟）
```bash
1. 打开 Chrome 浏览器
2. 地址栏输入：chrome://extensions/
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 my-request-modifier 文件夹
6. 完成！扩展图标会出现在工具栏
```

### 2. 创建第一个规则（2分钟）
```bash
1. 点击扩展图标
2. 点击"Add Rule"按钮
3. 填写：
   - 规则名称：测试规则
   - URL模式：*test.html*
   - 自定义函数：
     console.log('拦截到请求:', response.url);
4. 点击"Save Rule"
```

### 3. 测试功能（1分钟）
```bash
1. 打开项目中的 test.html 文件
2. 按 F12 打开开发者工具
3. 点击测试按钮
4. 在控制台查看日志输出
```

## 🎯 常用功能示例

### URL重写
```javascript
// 规则配置
URL模式: https://example.com/*
重写URL: https://proxy.example.com/$1
```

### 编码检测
```javascript
// 自定义函数
if (response.headers['content-type'].includes('text/html')) {
  const charset = utils.detectCharset(response.headers, response.body);
  console.log('页面编码:', charset);
  
  if (charset === 'gbk') {
    console.log('检测到GBK编码，进行特殊处理');
  }
}
```

### 内容处理
```javascript
// 自定义函数
if (response.body) {
  console.log('响应内容长度:', response.body.length);
  console.log('响应状态:', response.status);
  console.log('响应头:', response.headers);
  
  // 检测编码问题
  if (response.body.includes('Ã¡') || response.body.includes('â€™')) {
    console.log('检测到可能的编码问题');
  }
}
```

## 📋 功能清单

- ✅ **请求拦截**：自动拦截匹配的网络请求
- ✅ **URL重写**：支持正则表达式和通配符
- ✅ **编码检测**：自动识别GBK、UTF-8等编码
- ✅ **自定义函数**：JavaScript处理响应内容
- ✅ **日志记录**：完整的请求历史记录
- ✅ **规则管理**：启用/禁用、导入/导出
- ✅ **现代UI**：卡片式设计，响应式布局

## 🔧 高级用法

### 批量规则管理
1. 进入选项页面（Advanced Settings）
2. 使用导入/导出功能管理规则
3. 支持JSON格式的配置文件

### 调试模式
1. 选项页面开启"调试模式"
2. 查看详细的执行日志
3. 在浏览器控制台查看错误信息

### 模板使用
1. 选项页面选择"Custom Function Templates"
2. 选择预设模板
3. 复制代码到规则中使用

## ⚠️ 注意事项

- 扩展会拦截网络请求，请在可信网站使用
- 自定义函数语法错误会导致规则失效
- 建议定期导出配置进行备份
- 大量规则可能影响浏览器性能

## 🆘 常见问题

**Q: 规则不生效？**
A: 检查URL模式是否正确，确保扩展已启用

**Q: 控制台没有日志？**
A: 确保自定义函数语法正确，开启调试模式

**Q: 编码问题没解决？**
A: 检查编码检测设置，查看日志中的编码信息

---

更多详细信息请查看 [README.md](README.md) 和 [INSTALL.md](INSTALL.md)
