/* Modern UI Styles for Request Modifier Pro */

:root {
  --primary-color: #4285f4;
  --primary-dark: #3367d6;
  --secondary-color: #34a853;
  --warning-color: #fbbc04;
  --danger-color: #ea4335;
  --background-color: #f8f9fa;
  --surface-color: #ffffff;
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --border-color: #dadce0;
  --shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-hover: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --border-radius: 8px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.5;
  min-width: 400px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.popup-container {
  width: 400px;
  max-height: 600px;
  overflow-y: auto;
}

/* Card Styles */
.card {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-hover);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.card-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 4px;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 12px 20px;
  background-color: var(--background-color);
  border-top: 1px solid var(--border-color);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  gap: 8px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--text-primary);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.btn-outline:hover {
  background-color: var(--background-color);
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: 'Consolas', 'Monaco', monospace;
}

/* Status Indicators */
.status {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  gap: 4px;
}

.status-active {
  background-color: rgba(52, 168, 83, 0.1);
  color: var(--secondary-color);
}

.status-inactive {
  background-color: rgba(95, 99, 104, 0.1);
  color: var(--text-secondary);
}

.status-error {
  background-color: rgba(234, 67, 53, 0.1);
  color: var(--danger-color);
}

/* Log Entry Styles */
.log-entry {
  padding: 12px;
  border-left: 4px solid var(--border-color);
  margin-bottom: 8px;
  background: var(--surface-color);
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.log-entry.success {
  border-left-color: var(--secondary-color);
}

.log-entry.error {
  border-left-color: var(--danger-color);
}

.log-entry.warning {
  border-left-color: var(--warning-color);
}

.log-timestamp {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.log-url {
  font-family: monospace;
  font-size: 13px;
  word-break: break-all;
  margin-bottom: 4px;
}

.log-details {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Toggle Switch */
.toggle {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: var(--transition);
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition);
  border-radius: 50%;
}

.toggle input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

.toggle input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

/* Rule Item Styles */
.rule-item {
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: 8px;
  background: var(--surface-color);
  transition: var(--transition);
}

.rule-item:hover {
  box-shadow: var(--shadow);
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rule-name {
  font-weight: 600;
  color: var(--text-primary);
}

.rule-pattern {
  font-family: monospace;
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--background-color);
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 4px;
}

.rule-actions {
  display: flex;
  gap: 4px;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.ml-2 { margin-left: 16px; }
.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-between { justify-content: space-between; }
.flex-center { justify-content: center; }
.align-center { align-items: center; }
.gap-1 { gap: 8px; }
.gap-2 { gap: 16px; }

/* Responsive */
@media (max-width: 480px) {
  .container {
    padding: 12px;
  }
  
  .popup-container {
    width: 100%;
    min-width: 320px;
  }
}
