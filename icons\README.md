# Icons

This folder should contain the extension icons:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

You can create these icons using any image editing software. The icons should represent the extension's functionality (e.g., a network/request modification symbol).

For now, you can use placeholder icons or remove the icons section from manifest.json if you don't have custom icons ready.
