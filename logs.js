// Logs page script for Request Modifier Pro

class LogsManager {
  constructor() {
    this.allLogs = [];
    this.filteredLogs = [];
    this.currentPage = 1;
    this.logsPerPage = 50;
    this.rules = [];
    
    this.init();
  }

  async init() {
    await this.loadData();
    this.setupEventListeners();
    this.setupFilters();
    this.updateUI();
  }

  async loadData() {
    try {
      // Get all logs
      const logsResponse = await this.sendMessage({ type: 'getLogs' });
      this.allLogs = (logsResponse.logs || []).reverse(); // Most recent first
      this.filteredLogs = [...this.allLogs];

      // Get rules for filter dropdown
      const rulesResponse = await this.sendMessage({ type: 'getRules' });
      this.rules = rulesResponse.rules || [];
    } catch (error) {
      console.error('Failed to load logs:', error);
    }
  }

  setupEventListeners() {
    // Action buttons
    document.getElementById('refreshLogsBtn').addEventListener('click', () => this.refreshLogs());
    document.getElementById('clearAllLogsBtn').addEventListener('click', () => this.clearAllLogs());
    document.getElementById('exportLogsBtn').addEventListener('click', () => this.exportLogs());
    
    // Filter buttons
    document.getElementById('applyFiltersBtn').addEventListener('click', () => this.applyFilters());
    
    // Pagination
    document.getElementById('prevPageBtn').addEventListener('click', () => this.previousPage());
    document.getElementById('nextPageBtn').addEventListener('click', () => this.nextPage());
    
    // Modal
    document.getElementById('closeLogDetailBtn').addEventListener('click', () => this.hideLogDetail());
    
    // Close modal when clicking outside
    document.getElementById('logDetailModal').addEventListener('click', (e) => {
      if (e.target.id === 'logDetailModal') {
        this.hideLogDetail();
      }
    });

    // Real-time filter on URL input
    document.getElementById('urlFilter').addEventListener('input', () => {
      clearTimeout(this.filterTimeout);
      this.filterTimeout = setTimeout(() => this.applyFilters(), 300);
    });
  }

  setupFilters() {
    // Populate rule filter dropdown
    const ruleFilter = document.getElementById('ruleFilter');
    const uniqueRules = [...new Set(this.allLogs.map(log => log.details.rule).filter(Boolean))];
    
    uniqueRules.forEach(ruleName => {
      const option = document.createElement('option');
      option.value = ruleName;
      option.textContent = ruleName;
      ruleFilter.appendChild(option);
    });

    // Set default date range (last 7 days)
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('toDate').value = today.toISOString().split('T')[0];
    document.getElementById('fromDate').value = weekAgo.toISOString().split('T')[0];
  }

  async refreshLogs() {
    await this.loadData();
    this.applyFilters();
  }

  async clearAllLogs() {
    if (confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
      try {
        await this.sendMessage({ type: 'clearLogs' });
        this.allLogs = [];
        this.filteredLogs = [];
        this.updateUI();
      } catch (error) {
        console.error('Failed to clear logs:', error);
        alert('Failed to clear logs');
      }
    }
  }

  exportLogs() {
    const dataStr = JSON.stringify(this.filteredLogs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `request-modifier-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  }

  applyFilters() {
    const urlFilter = document.getElementById('urlFilter').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    const ruleFilter = document.getElementById('ruleFilter').value;
    const fromDate = document.getElementById('fromDate').value;
    const toDate = document.getElementById('toDate').value;

    this.filteredLogs = this.allLogs.filter(log => {
      // URL filter
      if (urlFilter && !log.url.toLowerCase().includes(urlFilter) && 
          !log.originalUrl.toLowerCase().includes(urlFilter)) {
        return false;
      }

      // Type filter
      if (typeFilter && log.type !== typeFilter) {
        return false;
      }

      // Rule filter
      if (ruleFilter && log.details.rule !== ruleFilter) {
        return false;
      }

      // Date filters
      const logDate = new Date(log.timestamp).toISOString().split('T')[0];
      if (fromDate && logDate < fromDate) {
        return false;
      }
      if (toDate && logDate > toDate) {
        return false;
      }

      return true;
    });

    this.currentPage = 1;
    this.updateUI();
  }

  updateUI() {
    this.updateStats();
    this.updateLogsList();
    this.updatePagination();
  }

  updateStats() {
    const totalLogs = document.getElementById('totalLogs');
    const todayLogs = document.getElementById('todayLogs');

    totalLogs.textContent = `${this.filteredLogs.length} total logs`;

    const today = new Date().toDateString();
    const todayCount = this.filteredLogs.filter(log => 
      new Date(log.timestamp).toDateString() === today
    ).length;
    
    todayLogs.textContent = `${todayCount} today`;
  }

  updateLogsList() {
    const logsList = document.getElementById('logsList');
    
    if (this.filteredLogs.length === 0) {
      logsList.innerHTML = `
        <div class="text-center" style="color: var(--text-secondary); padding: 40px;">
          No logs found matching the current filters
        </div>
      `;
      return;
    }

    const startIndex = (this.currentPage - 1) * this.logsPerPage;
    const endIndex = startIndex + this.logsPerPage;
    const pageLog = this.filteredLogs.slice(startIndex, endIndex);

    logsList.innerHTML = pageLog.map(log => this.renderLogEntry(log)).join('');

    // Add click listeners to log entries
    logsList.querySelectorAll('.log-entry').forEach(entry => {
      entry.addEventListener('click', () => {
        const logId = entry.dataset.logId;
        this.showLogDetail(logId);
      });
    });
  }

  renderLogEntry(log) {
    const typeIcon = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };

    return `
      <div class="log-entry ${log.type}" data-log-id="${log.id}" style="cursor: pointer;">
        <div class="flex flex-between align-center mb-1">
          <div class="log-timestamp">
            ${typeIcon[log.type] || '📝'} ${Utils.formatTimestamp(log.timestamp)}
          </div>
          <div class="status status-${log.type}">${log.type.toUpperCase()}</div>
        </div>
        <div class="log-url">${Utils.escapeHtml(log.url)}</div>
        ${log.originalUrl !== log.url ? `
          <div class="log-details" style="font-size: 11px; color: var(--text-secondary);">
            Original: ${Utils.escapeHtml(Utils.truncateText(log.originalUrl, 60))}
          </div>
        ` : ''}
        <div class="log-details">
          ${log.details.rule ? `Rule: ${Utils.escapeHtml(log.details.rule)}` : ''}
          ${log.details.type ? ` | ${Utils.escapeHtml(log.details.type)}` : ''}
          ${log.details.method ? ` | ${log.details.method}` : ''}
          ${log.details.charset ? ` | Charset: ${log.details.charset}` : ''}
        </div>
      </div>
    `;
  }

  updatePagination() {
    const pagination = document.getElementById('pagination');
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');
    const pageInfo = document.getElementById('pageInfo');

    const totalPages = Math.ceil(this.filteredLogs.length / this.logsPerPage);

    if (totalPages <= 1) {
      pagination.style.display = 'none';
      return;
    }

    pagination.style.display = 'flex';
    pageInfo.textContent = `Page ${this.currentPage} of ${totalPages}`;
    
    prevBtn.disabled = this.currentPage === 1;
    nextBtn.disabled = this.currentPage === totalPages;
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updateUI();
    }
  }

  nextPage() {
    const totalPages = Math.ceil(this.filteredLogs.length / this.logsPerPage);
    if (this.currentPage < totalPages) {
      this.currentPage++;
      this.updateUI();
    }
  }

  showLogDetail(logId) {
    const log = this.filteredLogs.find(l => l.id === logId);
    if (!log) return;

    const modal = document.getElementById('logDetailModal');
    const content = document.getElementById('logDetailContent');

    content.innerHTML = `
      <div class="form-group">
        <label class="form-label">Timestamp</label>
        <div class="form-input" style="background: var(--background-color);">
          ${Utils.formatTimestamp(log.timestamp)}
        </div>
      </div>
      
      <div class="form-group">
        <label class="form-label">Type</label>
        <div class="status status-${log.type}">${log.type.toUpperCase()}</div>
      </div>
      
      <div class="form-group">
        <label class="form-label">URL</label>
        <div class="form-input" style="background: var(--background-color); word-break: break-all;">
          ${Utils.escapeHtml(log.url)}
        </div>
      </div>
      
      ${log.originalUrl !== log.url ? `
        <div class="form-group">
          <label class="form-label">Original URL</label>
          <div class="form-input" style="background: var(--background-color); word-break: break-all;">
            ${Utils.escapeHtml(log.originalUrl)}
          </div>
        </div>
      ` : ''}
      
      <div class="form-group">
        <label class="form-label">Domain</label>
        <div class="form-input" style="background: var(--background-color);">
          ${Utils.escapeHtml(log.domain)}
        </div>
      </div>
      
      <div class="form-group">
        <label class="form-label">Details</label>
        <div class="form-textarea" style="background: var(--background-color); min-height: 120px;">
          ${JSON.stringify(log.details, null, 2)}
        </div>
      </div>
    `;

    modal.style.display = 'flex';
  }

  hideLogDetail() {
    document.getElementById('logDetailModal').style.display = 'none';
  }

  sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }
}

// Initialize logs manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.logsManager = new LogsManager();
});
