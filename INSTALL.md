# Request Modifier Pro 安装指南

## 快速安装

### 1. 准备工作
- 确保使用 Chrome 88+ 或基于 Chromium 的浏览器（推荐Chrome 100+）
- 确保已下载完整的项目文件
- **重要**：此扩展使用 Manifest V3，与旧版本API不兼容

### 2. 安装步骤

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开启开发者模式

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择项目文件夹 `my-request-modifier`
   - 点击"选择文件夹"

4. **验证安装**
   - 扩展应该出现在扩展列表中
   - 浏览器工具栏应该显示扩展图标
   - 点击图标应该弹出扩展界面

## 首次使用

### 1. 基本设置
1. 点击扩展图标打开弹出窗口
2. 确保主开关处于"启用"状态
3. 点击"Advanced Settings"进入详细配置

### 2. 创建第一个规则
1. 在弹出窗口点击"Add Rule"
2. 填写规则信息：
   - **规则名称**：`测试规则`
   - **URL模式**：`*test.html*`
   - **自定义函数**：
   ```javascript
   console.log('请求被拦截:', response.url);
   console.log('响应状态:', response.status);
   ```
3. 点击"Save Rule"保存

### 3. 测试功能
1. 打开项目中的 `test.html` 文件
2. 按 F12 打开开发者工具
3. 在控制台中应该看到扩展的日志输出
4. 点击测试页面上的按钮测试各种功能

## 常见问题

### Q: 扩展安装后没有图标显示
**A:** 检查以下几点：
- 确保开发者模式已启用
- 检查扩展是否在扩展列表中显示为"已启用"
- 尝试刷新扩展页面或重启浏览器

### Q: 规则不生效
**A:** 检查以下几点：
- 确保扩展主开关已启用
- 检查URL模式是否正确匹配目标网站
- 查看扩展日志是否有错误信息
- 在选项页面开启调试模式获取详细日志
- **Manifest V3限制**：URL重写现在使用declarativeNetRequest API，某些复杂的正则表达式可能不支持

### Q: 自定义函数报错
**A:** 检查以下几点：
- 确保JavaScript语法正确
- 使用选项页面的模板作为参考
- 检查浏览器控制台的错误信息
- 确保使用了正确的API（response、request、utils）

### Q: 编码问题没有解决
**A:** 检查以下几点：
- 确保在选项页面启用了"自动检测编码"
- 检查目标网站的字符编码设置
- 尝试手动指定默认字符编码
- 查看日志中的编码检测信息

## 高级配置

### 1. 编码设置
在选项页面可以配置：
- 默认字符编码
- 自动检测编码开关
- Mojibake修复开关

### 2. 日志设置
可以配置：
- 最大日志条数（100-10000）
- 日志保留天数（1-365天）
- 调试模式开关

### 3. 规则管理
支持：
- 批量导入/导出规则
- 规则启用/禁用
- 规则编辑和删除

## 开发调试

### 1. 启用调试模式
1. 进入选项页面
2. 开启"调试模式"
3. 重新加载扩展

### 2. 查看日志
- **扩展日志**：在日志页面查看
- **控制台日志**：按F12查看浏览器控制台
- **后台日志**：在扩展管理页面点击"检查视图"

### 3. 常用调试技巧
- 使用 `console.log()` 在自定义函数中输出调试信息
- 在选项页面测试自定义函数语法
- 使用测试页面验证各种功能
- 查看网络面板确认请求是否被拦截

## 卸载扩展

1. 进入 `chrome://extensions/`
2. 找到 "Request Modifier Pro"
3. 点击"移除"按钮
4. 确认删除

**注意**：卸载扩展会删除所有保存的规则和设置。

## 更新扩展

当有新版本时：
1. 下载新版本文件
2. 在扩展管理页面点击"重新加载"按钮
3. 或者删除旧版本后重新安装

**注意**：更新前建议导出配置以防数据丢失。

## 技术支持

如果遇到问题：
1. 查看本文档的常见问题部分
2. 检查浏览器控制台的错误信息
3. 开启调试模式获取详细日志
4. 在GitHub项目页面提交Issue

---

**重要提醒**：此扩展会拦截和修改网络请求，请仅在可信任的网站上使用，并注意保护个人隐私和数据安全。
