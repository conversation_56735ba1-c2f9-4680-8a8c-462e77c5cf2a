// Utility functions for Request Modifier Pro

class Utils {
  // Detect charset from response headers or content
  static detectCharset(headers, content) {
    // Check Content-Type header first
    const contentType = headers['content-type'] || headers['Content-Type'] || '';
    const charsetMatch = contentType.match(/charset=([^;]+)/i);
    if (charsetMatch) {
      return charsetMatch[1].toLowerCase();
    }

    // Check HTML meta tags
    if (content) {
      const metaCharsetMatch = content.match(/<meta[^>]+charset=["']?([^"'>]+)/i);
      if (metaCharsetMatch) {
        return metaCharsetMatch[1].toLowerCase();
      }

      const metaHttpEquivMatch = content.match(/<meta[^>]+http-equiv=["']?content-type["'][^>]+content=["'][^"'>]*charset=([^"'>]+)/i);
      if (metaHttpEquivMatch) {
        return metaHttpEquivMatch[1].toLowerCase();
      }
    }

    // Default to UTF-8
    return 'utf-8';
  }

  // Convert encoding names to standard format
  static normalizeCharset(charset) {
    const charsetMap = {
      'gb2312': 'gbk',
      'gb18030': 'gbk',
      'iso-8859-1': 'latin1',
      'windows-1252': 'latin1'
    };
    
    const normalized = charset.toLowerCase().replace(/[_-]/g, '');
    return charsetMap[normalized] || charset;
  }

  // Decode text with specified charset
  static async decodeText(arrayBuffer, charset) {
    try {
      const normalizedCharset = this.normalizeCharset(charset);
      
      if (normalizedCharset === 'gbk' || normalizedCharset === 'gb2312') {
        // For GBK encoding, we need to use a polyfill or TextDecoder with fallback
        try {
          const decoder = new TextDecoder('gbk');
          return decoder.decode(arrayBuffer);
        } catch (e) {
          // Fallback to UTF-8 if GBK is not supported
          console.warn('GBK decoding not supported, falling back to UTF-8');
          const decoder = new TextDecoder('utf-8');
          return decoder.decode(arrayBuffer);
        }
      } else {
        const decoder = new TextDecoder(normalizedCharset);
        return decoder.decode(arrayBuffer);
      }
    } catch (error) {
      console.error('Decoding error:', error);
      // Fallback to UTF-8
      const decoder = new TextDecoder('utf-8');
      return decoder.decode(arrayBuffer);
    }
  }

  // Generate unique ID
  static generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  // Format timestamp
  static formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString();
  }

  // Validate URL pattern
  static isValidUrlPattern(pattern) {
    try {
      new RegExp(pattern);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Match URL against pattern
  static matchUrl(url, pattern) {
    try {
      if (pattern.includes('*')) {
        // Convert wildcard pattern to regex
        const regexPattern = pattern
          .replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
          .replace(/\\\*/g, '.*');
        return new RegExp(regexPattern).test(url);
      } else {
        return new RegExp(pattern).test(url);
      }
    } catch (e) {
      return false;
    }
  }

  // Safe JSON parse
  static safeJsonParse(str, defaultValue = null) {
    try {
      return JSON.parse(str);
    } catch (e) {
      return defaultValue;
    }
  }

  // Escape HTML
  static escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // Truncate text
  static truncateText(text, maxLength = 100) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  // Get domain from URL
  static getDomain(url) {
    try {
      return new URL(url).hostname;
    } catch (e) {
      return url;
    }
  }

  // Storage helpers
  static async getStorageData(keys) {
    return new Promise((resolve) => {
      chrome.storage.local.get(keys, resolve);
    });
  }

  static async setStorageData(data) {
    return new Promise((resolve) => {
      chrome.storage.local.set(data, resolve);
    });
  }

  // Log entry creation
  static createLogEntry(type, url, originalUrl, details = {}) {
    return {
      id: this.generateId(),
      timestamp: Date.now(),
      type: type, // 'success', 'error', 'warning', 'info'
      url: url,
      originalUrl: originalUrl,
      domain: this.getDomain(originalUrl),
      details: details
    };
  }

  // Custom function execution with error handling
  static async executeCustomFunction(functionCode, response, request) {
    try {
      // Create a safe execution context
      const customFunction = new Function('response', 'request', 'utils', functionCode);
      const result = await customFunction(response, request, {
        detectCharset: this.detectCharset,
        decodeText: this.decodeText,
        escapeHtml: this.escapeHtml,
        truncateText: this.truncateText
      });
      return { success: true, result };
    } catch (error) {
      console.error('Custom function execution error:', error);
      return { success: false, error: error.message };
    }
  }

  // Validate custom function code
  static validateCustomFunction(code) {
    try {
      new Function('response', 'request', 'utils', code);
      return { valid: true };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  // Format file size
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Get response type from headers
  static getResponseType(headers) {
    const contentType = headers['content-type'] || headers['Content-Type'] || '';
    if (contentType.includes('text/html')) return 'HTML';
    if (contentType.includes('application/json')) return 'JSON';
    if (contentType.includes('text/css')) return 'CSS';
    if (contentType.includes('application/javascript')) return 'JavaScript';
    if (contentType.includes('text/')) return 'Text';
    if (contentType.includes('image/')) return 'Image';
    return 'Other';
  }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Utils;
} else if (typeof window !== 'undefined') {
  window.Utils = Utils;
}
