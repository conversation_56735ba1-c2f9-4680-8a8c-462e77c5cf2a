// Content script for Request Modifier Pro

class ContentScriptManager {
  constructor() {
    this.init();
  }

  init() {
    // Inject utils script
    this.injectUtils();
    
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });

    // Monitor page encoding and charset
    this.monitorPageEncoding();
  }

  injectUtils() {
    // Inject utils.js if not already present
    if (!window.Utils) {
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('utils.js');
      script.onload = () => {
        console.log('Request Modifier Pro: Utils loaded');
      };
      (document.head || document.documentElement).appendChild(script);
    }
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'executeCustomFunction':
        this.executeCustomFunction(message.functionCode, message.response, message.request)
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        break;
        
      case 'getPageInfo':
        sendResponse(this.getPageInfo());
        break;
        
      case 'detectEncoding':
        sendResponse(this.detectPageEncoding());
        break;
    }
  }

  async executeCustomFunction(functionCode, response, request) {
    try {
      // Ensure Utils is available
      if (!window.Utils) {
        throw new Error('Utils not available');
      }

      // Create enhanced response object with page content
      const enhancedResponse = {
        ...response,
        body: document.documentElement.outerHTML,
        document: document,
        window: window
      };

      // Execute the custom function
      const result = await Utils.executeCustomFunction(functionCode, enhancedResponse, request);
      
      // Send result back to background script
      chrome.runtime.sendMessage({
        type: 'customFunctionResult',
        success: result.success,
        url: response.url,
        rule: request.rule,
        error: result.error,
        result: result.result
      });

      return result;
    } catch (error) {
      console.error('Content script custom function error:', error);
      return { success: false, error: error.message };
    }
  }

  getPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      charset: document.characterSet,
      contentType: document.contentType,
      encoding: this.detectPageEncoding(),
      hasMetaCharset: !!document.querySelector('meta[charset]'),
      hasMetaHttpEquiv: !!document.querySelector('meta[http-equiv="content-type"]')
    };
  }

  detectPageEncoding() {
    // Check meta charset
    const metaCharset = document.querySelector('meta[charset]');
    if (metaCharset) {
      return metaCharset.getAttribute('charset');
    }

    // Check meta http-equiv
    const metaHttpEquiv = document.querySelector('meta[http-equiv="content-type"]');
    if (metaHttpEquiv) {
      const content = metaHttpEquiv.getAttribute('content');
      const charsetMatch = content.match(/charset=([^;]+)/i);
      if (charsetMatch) {
        return charsetMatch[1];
      }
    }

    // Return document charset
    return document.characterSet || 'UTF-8';
  }

  monitorPageEncoding() {
    // Check if page has encoding issues
    const hasEncodingIssues = this.checkForEncodingIssues();
    
    if (hasEncodingIssues) {
      console.log('Request Modifier Pro: Potential encoding issues detected');
      
      // Send encoding info to background script
      chrome.runtime.sendMessage({
        type: 'encodingIssueDetected',
        url: window.location.href,
        detectedCharset: this.detectPageEncoding(),
        documentCharset: document.characterSet,
        hasIssues: hasEncodingIssues
      });
    }
  }

  checkForEncodingIssues() {
    // Look for common encoding issue indicators
    const bodyText = document.body ? document.body.textContent : '';
    
    // Check for common mojibake patterns
    const mojibakePatterns = [
      /[Ã¢â‚¬â„¢]/g, // UTF-8 interpreted as Latin-1
      /[â€™â€œâ€]/g,   // Smart quotes issues
      /[Ã¡Ã©Ã­Ã³Ãº]/g, // Accented characters issues
      /[ï¿½]/g,        // Replacement character
      /[Â]/g           // Non-breaking space issues
    ];

    return mojibakePatterns.some(pattern => pattern.test(bodyText));
  }

  // Helper method to fix encoding issues (if needed)
  fixEncodingIssues() {
    // This would be called by custom functions to fix encoding
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node);
    }

    textNodes.forEach(textNode => {
      const originalText = textNode.textContent;
      const fixedText = this.fixMojibake(originalText);
      if (fixedText !== originalText) {
        textNode.textContent = fixedText;
      }
    });
  }

  fixMojibake(text) {
    // Basic mojibake fixes
    const fixes = {
      'Ã¡': 'á',
      'Ã©': 'é',
      'Ã­': 'í',
      'Ã³': 'ó',
      'Ãº': 'ú',
      'Ã±': 'ñ',
      'Ã¼': 'ü',
      'â€™': "'",
      'â€œ': '"',
      'â€': '"',
      'â€"': '—',
      'â€"': '–'
    };

    let fixedText = text;
    for (const [wrong, correct] of Object.entries(fixes)) {
      fixedText = fixedText.replace(new RegExp(wrong, 'g'), correct);
    }

    return fixedText;
  }
}

// Initialize content script manager
const contentScriptManager = new ContentScriptManager();
