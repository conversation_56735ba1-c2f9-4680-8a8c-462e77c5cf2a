# Request Modifier Pro

一个现代化的Chrome扩展，用于拦截和修改网络请求，具备智能编码处理和自定义响应函数功能。

## 功能特性

### 🎨 现代化UI设计
- 简洁美观的卡片式布局
- 一致的配色方案和动画效果
- 响应式设计，适配不同屏幕尺寸

### 📋 请求日志系统
- 记录所有被规则匹配的请求
- 详细的历史记录查看
- 支持按URL、类型、规则等多维度筛选
- 可导出日志数据

### 🔤 智能编码处理
- 自动检测响应头中的charset（GBK、UTF-8等）
- 智能解码，解决乱码问题
- 支持多种字符编码格式
- 自动修复常见的编码错误

### ⚙️ 自定义响应处理
- 支持编写自定义JavaScript函数
- 响应对象作为函数参数
- 内置工具函数库
- 预设模板快速开始

### 🔧 高级功能
- URL重写规则支持
- 正则表达式和通配符匹配
- 规则的启用/禁用管理
- 配置导入/导出

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹

## 使用指南

### 基本使用

1. **启用扩展**：点击扩展图标，确保主开关处于开启状态
2. **添加规则**：点击"Add Rule"按钮创建新的修改规则
3. **查看日志**：点击"View Logs"查看请求处理历史

### 创建规则

1. **规则名称**：为规则起一个描述性的名称
2. **URL模式**：
   - 使用通配符：`https://example.com/*`
   - 使用正则表达式：`.*\.example\.com.*`
3. **重写URL**（可选）：
   - 支持捕获组：`https://proxy.example.com/$1`
4. **自定义函数**（可选）：编写JavaScript代码处理响应

### 自定义函数示例

```javascript
// 修复编码问题
if (response.headers['content-type'].includes('text/html')) {
  const charset = utils.detectCharset(response.headers, response.body);
  console.log('检测到字符编码:', charset);
  
  if (charset === 'gbk') {
    console.log('处理GBK编码内容:', response.url);
    // 在这里添加自定义处理逻辑
  }
}
```

```javascript
// 内容替换
if (response.body && response.headers['content-type'].includes('text/html')) {
  const originalContent = response.body;
  const modifiedContent = originalContent.replace(/旧文本/g, '新文本');
  
  if (modifiedContent !== originalContent) {
    console.log('内容已替换:', response.url);
    // 注意：在content script环境中可以直接修改DOM
  }
}
```

## 文件结构

```
my-request-modifier/
├── manifest.json          # 扩展配置文件
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── popup.html            # 弹出窗口界面
├── popup.js              # 弹出窗口逻辑
├── options.html          # 选项页面
├── options.js            # 选项页面逻辑
├── logs.html             # 日志查看页面
├── logs.js               # 日志页面逻辑
├── styles.css            # 统一样式文件
├── utils.js              # 工具函数库
├── test.html             # 测试页面
├── INSTALL.md            # 安装指南
└── README.md             # 说明文档
```

## 技术特性

- **Manifest V3**：使用最新的Chrome扩展API
- **现代JavaScript**：ES6+语法和异步编程
- **模块化设计**：清晰的代码结构和职责分离
- **错误处理**：完善的异常捕获和用户反馈
- **性能优化**：高效的请求处理和内存管理

## 权限说明

- `declarativeNetRequest`：用于拦截和修改网络请求
- `storage`：保存规则和设置
- `activeTab`：访问当前标签页
- `scripting`：注入内容脚本
- `webRequest`：监听网络请求
- `<all_urls>`：访问所有网站（用于请求拦截）

## 开发说明

### 调试模式

在选项页面开启"调试模式"可以获得更详细的日志信息，便于问题排查。

### 自定义函数API

自定义函数接收以下参数：

- `response`：响应对象，包含URL、状态码、头部、内容等
- `request`：请求对象，包含URL、方法等信息
- `utils`：工具函数库，提供编码检测、文本处理等功能

### 工具函数

- `utils.detectCharset(headers, content)`：检测字符编码
- `utils.decodeText(arrayBuffer, charset)`：解码文本
- `utils.escapeHtml(text)`：HTML转义
- `utils.truncateText(text, maxLength)`：截断文本

## 更新日志

### v1.0.0
- 初始版本发布
- 基础请求拦截和重写功能
- 智能编码检测和处理
- 现代化UI设计
- 完整的日志系统
- 自定义函数支持

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 支持

如果您在使用过程中遇到问题，请：

1. 查看浏览器控制台的错误信息
2. 开启调试模式获取详细日志
3. 在GitHub上提交Issue

---

**注意**：此扩展需要Chrome 88+版本才能正常运行。
