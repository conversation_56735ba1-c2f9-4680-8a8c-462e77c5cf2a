<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Modifier Pro - 测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #3367d6;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { border-left: 4px solid #34a853; }
        .error { border-left: 4px solid #ea4335; }
        .warning { border-left: 4px solid #fbbc04; }
    </style>
</head>
<body>
    <div class="card">
        <h1>Request Modifier Pro 测试页面</h1>
        <p>这个页面用于测试扩展的各种功能。请确保已经安装并启用了 Request Modifier Pro 扩展。</p>
    </div>

    <div class="card">
        <h2>功能测试</h2>
        <button class="btn" onclick="testAjaxRequest()">测试 AJAX 请求</button>
        <button class="btn" onclick="testFetchRequest()">测试 Fetch 请求</button>
        <button class="btn" onclick="testImageRequest()">测试图片请求</button>
        <button class="btn" onclick="testEncodingIssue()">模拟编码问题</button>
        <button class="btn" onclick="clearLogs()">清除日志</button>
    </div>

    <div class="card">
        <h2>测试日志</h2>
        <div id="testLogs"></div>
    </div>

    <div class="card">
        <h2>扩展使用说明</h2>
        <ol>
            <li><strong>安装扩展</strong>：在Chrome中加载解压的扩展程序</li>
            <li><strong>创建规则</strong>：点击扩展图标，添加新规则</li>
            <li><strong>测试规则</strong>：
                <ul>
                    <li>URL模式示例：<code>*test.html*</code> 或 <code>.*localhost.*</code></li>
                    <li>重写URL示例：<code>https://httpbin.org/get?original=$1</code></li>
                </ul>
            </li>
            <li><strong>自定义函数示例</strong>：
                <pre style="background: #f0f0f0; padding: 10px; border-radius: 5px;">
// 记录请求信息
console.log('请求URL:', response.url);
console.log('响应状态:', response.status);
console.log('响应头:', response.headers);

// 检测编码
if (response.headers['content-type']) {
    const charset = utils.detectCharset(response.headers, response.body);
    console.log('检测到的字符编码:', charset);
}

// 处理HTML内容
if (response.body && response.headers['content-type'].includes('text/html')) {
    console.log('处理HTML响应');
    // 在这里添加自定义逻辑
}
                </pre>
            </li>
        </ol>
    </div>

    <div class="card">
        <h2>常见测试场景</h2>
        <h3>1. URL重写测试</h3>
        <p>创建规则匹配当前页面，将请求重定向到其他URL：</p>
        <ul>
            <li>URL模式：<code>*test.html*</code></li>
            <li>重写URL：<code>https://httpbin.org/get</code></li>
        </ul>

        <h3>2. 编码检测测试</h3>
        <p>创建规则检测页面编码：</p>
        <ul>
            <li>URL模式：<code>*test.html*</code></li>
            <li>自定义函数：使用编码检测代码</li>
        </ul>

        <h3>3. 请求日志测试</h3>
        <p>创建规则记录所有请求：</p>
        <ul>
            <li>URL模式：<code>*</code></li>
            <li>自定义函数：记录请求详情</li>
        </ul>
    </div>

    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('testLogs');
            const logEntry = document.createElement('div');
            logEntry.className = `log ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('testLogs').innerHTML = '';
            log('日志已清除');
        }

        async function testAjaxRequest() {
            log('开始测试 AJAX 请求...');
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', 'https://httpbin.org/get?test=ajax');
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        log('AJAX 请求成功: ' + xhr.status, 'success');
                    } else {
                        log('AJAX 请求失败: ' + xhr.status, 'error');
                    }
                };
                xhr.onerror = function() {
                    log('AJAX 请求错误', 'error');
                };
                xhr.send();
            } catch (error) {
                log('AJAX 请求异常: ' + error.message, 'error');
            }
        }

        async function testFetchRequest() {
            log('开始测试 Fetch 请求...');
            try {
                const response = await fetch('https://httpbin.org/get?test=fetch');
                if (response.ok) {
                    const data = await response.json();
                    log('Fetch 请求成功: ' + response.status, 'success');
                    log('响应数据: ' + JSON.stringify(data.args));
                } else {
                    log('Fetch 请求失败: ' + response.status, 'error');
                }
            } catch (error) {
                log('Fetch 请求异常: ' + error.message, 'error');
            }
        }

        function testImageRequest() {
            log('开始测试图片请求...');
            const img = new Image();
            img.onload = function() {
                log('图片加载成功', 'success');
            };
            img.onerror = function() {
                log('图片加载失败', 'error');
            };
            img.src = 'https://httpbin.org/image/png?test=image&t=' + Date.now();
        }

        function testEncodingIssue() {
            log('模拟编码问题...');
            // 创建一些可能有编码问题的文本
            const problematicText = 'Ã¡Ã©Ã­Ã³Ãº â€™â€œâ€ ï¿½';
            const div = document.createElement('div');
            div.innerHTML = `<p>编码问题示例: ${problematicText}</p>`;
            document.body.appendChild(div);
            log('已添加编码问题文本，扩展应该能检测到', 'warning');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面已加载');
            log('请确保 Request Modifier Pro 扩展已安装并启用');
            
            // 检测扩展是否注入了工具函数
            if (window.Utils) {
                log('检测到扩展工具函数，扩展运行正常', 'success');
            } else {
                log('未检测到扩展工具函数，请检查扩展是否正确安装', 'warning');
            }
        });

        // 监听扩展消息
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
                log('收到扩展消息: ' + JSON.stringify(message));
            });
        }
    </script>
</body>
</html>
