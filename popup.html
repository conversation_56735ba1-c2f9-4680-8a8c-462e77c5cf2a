<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Request Modifier Pro</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="popup-container">
    <!-- Header -->
    <div class="card">
      <div class="card-header">
        <h1 class="card-title">Request Modifier Pro</h1>
        <div class="card-subtitle">Modern request interceptor with intelligent encoding</div>
      </div>
      <div class="card-body">
        <div class="flex flex-between align-center">
          <span>Extension Status</span>
          <label class="toggle">
            <input type="checkbox" id="enableToggle">
            <span class="toggle-slider"></span>
          </label>
        </div>
        <div class="mt-2">
          <span id="statusText" class="status status-active">Active</span>
          <span id="ruleCount" class="status status-inactive ml-2">0 rules</span>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Quick Actions</h2>
      </div>
      <div class="card-body">
        <div class="flex gap-1 mb-2">
          <button class="btn btn-primary flex-1" id="addRuleBtn">
            <span>➕</span> Add Rule
          </button>
          <button class="btn btn-secondary flex-1" id="viewLogsBtn">
            <span>📋</span> View Logs
          </button>
        </div>
        <button class="btn btn-outline" id="openOptionsBtn" style="width: 100%;">
          <span>⚙️</span> Advanced Settings
        </button>
      </div>
    </div>

    <!-- Recent Rules -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Active Rules</h2>
        <div class="card-subtitle">Recently used modification rules</div>
      </div>
      <div class="card-body">
        <div id="rulesList">
          <div class="text-center" style="color: var(--text-secondary); padding: 20px;">
            No rules configured yet
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Recent Activity</h2>
        <div class="card-subtitle">Latest request modifications</div>
      </div>
      <div class="card-body">
        <div id="recentLogs">
          <div class="text-center" style="color: var(--text-secondary); padding: 20px;">
            No recent activity
          </div>
        </div>
        <div class="card-footer">
          <button class="btn btn-outline btn-small" id="clearLogsBtn">Clear Logs</button>
        </div>
      </div>
    </div>


  </div>

  <script src="utils.js"></script>
  <script src="popup.js"></script>
</body>
</html>
