// Background script for Request Modifier Pro

class RequestModifier {
  constructor() {
    this.rules = [];
    this.logs = [];
    this.isEnabled = true;
    this.maxLogs = 1000;
    
    this.init();
  }

  async init() {
    // Load saved data
    await this.loadData();
    
    // Set up listeners
    this.setupListeners();
    
    // Clean old logs periodically
    setInterval(() => this.cleanOldLogs(), 60000); // Every minute
  }

  async loadData() {
    const data = await chrome.storage.local.get(['rules', 'logs', 'isEnabled']);
    this.rules = data.rules || [];
    this.logs = data.logs || [];
    this.isEnabled = data.isEnabled !== false;
  }

  async saveData() {
    await chrome.storage.local.set({
      rules: this.rules,
      logs: this.logs,
      isEnabled: this.isEnabled
    });
  }

  setupListeners() {
    // Web request listener for monitoring requests (non-blocking)
    chrome.webRequest.onBeforeRequest.addListener(
      (details) => this.handleRequest(details),
      { urls: ['*://*/*'] }
    );

    // Response listener for processing responses (non-blocking)
    chrome.webRequest.onHeadersReceived.addListener(
      (details) => this.handleResponse(details),
      { urls: ['*://*/*'] },
      ['responseHeaders']
    );

    // Message listener for popup communication
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep the message channel open for async responses
    });

    // Storage change listener
    chrome.storage.onChanged.addListener((changes) => {
      if (changes.rules) this.rules = changes.rules.newValue || [];
      if (changes.isEnabled) this.isEnabled = changes.isEnabled.newValue !== false;
    });

    // Setup declarative net request rules
    this.setupDeclarativeRules();
  }

  handleRequest(details) {
    if (!this.isEnabled) return;

    const matchedRule = this.findMatchingRule(details.url);
    if (!matchedRule || !matchedRule.enabled) return;

    // Log the request for monitoring
    this.addLog('info', details.url, details.url, {
      rule: matchedRule.name,
      type: 'Request Matched',
      method: details.method
    });

    // For URL rewriting, we'll use declarative net request rules
    // This is handled in setupDeclarativeRules()
  }

  async handleResponse(details) {
    if (!this.isEnabled) return {};

    const matchedRule = this.findMatchingRule(details.url);
    if (!matchedRule || !matchedRule.enabled || !matchedRule.customFunction) {
      return {};
    }

    try {
      // Get response body if needed for custom function
      if (matchedRule.customFunction.trim()) {
        // Note: In Manifest V3, we can't directly modify response body in background script
        // We'll inject a content script to handle this
        chrome.scripting.executeScript({
          target: { tabId: details.tabId },
          func: async function(ruleData, url, statusCode, headers, method) {
            const rule = ruleData;
            const utils = window.Utils;

            if (rule.customFunction && utils) {
              try {
                const response = {
                  url: url,
                  status: statusCode,
                  headers: headers,
                  body: document.documentElement.outerHTML
                };

                const result = await utils.executeCustomFunction(
                  rule.customFunction,
                  response,
                  { url: url, method: method }
                );

                chrome.runtime.sendMessage({
                  type: 'customFunctionResult',
                  success: result.success,
                  url: url,
                  rule: rule.name,
                  error: result.error
                });
              } catch (error) {
                chrome.runtime.sendMessage({
                  type: 'customFunctionResult',
                  success: false,
                  url: url,
                  rule: rule.name,
                  error: error.message
                });
              }
            }
          },
          args: [matchedRule, details.url, details.statusCode, this.headersToObject(details.responseHeaders), details.method]
        });
      }

      // Handle charset detection and modification
      const responseHeaders = details.responseHeaders || [];
      const contentType = this.getHeader(responseHeaders, 'content-type');
      
      if (contentType && contentType.includes('text/html')) {
        const charset = this.detectCharsetFromHeaders(responseHeaders);
        
        this.addLog('info', details.url, details.url, {
          rule: matchedRule.name,
          type: 'Response Processing',
          charset: charset,
          contentType: contentType
        });
      }

    } catch (error) {
      this.addLog('error', details.url, details.url, {
        rule: matchedRule.name,
        error: error.message,
        type: 'Response Processing Error'
      });
    }

    return {};
  }

  async handleMessage(message, _sender, sendResponse) {
    switch (message.type) {
      case 'getRules':
        sendResponse({ rules: this.rules });
        break;
        
      case 'saveRule':
        await this.saveRule(message.rule);
        sendResponse({ success: true });
        break;
        
      case 'deleteRule':
        await this.deleteRule(message.ruleId);
        sendResponse({ success: true });
        break;
        
      case 'toggleRule':
        await this.toggleRule(message.ruleId);
        sendResponse({ success: true });
        break;
        
      case 'getLogs':
        sendResponse({ logs: this.logs.slice(-100) }); // Return last 100 logs
        break;
        
      case 'clearLogs':
        this.logs = [];
        await this.saveData();
        sendResponse({ success: true });
        break;
        
      case 'getStatus':
        sendResponse({ enabled: this.isEnabled });
        break;
        
      case 'toggleEnabled':
        this.isEnabled = !this.isEnabled;
        await this.saveData();
        await this.updateDeclarativeRules();
        sendResponse({ enabled: this.isEnabled });
        break;

      case 'customFunctionResult':
        if (message.success) {
          this.addLog('success', message.url, message.url, {
            rule: message.rule,
            type: 'Custom Function Executed'
          });
        } else {
          this.addLog('error', message.url, message.url, {
            rule: message.rule,
            type: 'Custom Function Error',
            error: message.error
          });
        }
        break;
    }
  }

  findMatchingRule(url) {
    return this.rules.find(rule => {
      if (!rule.enabled) return false;
      
      try {
        if (rule.urlPattern.includes('*')) {
          const regexPattern = rule.urlPattern
            .replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
            .replace(/\\\*/g, '.*');
          return new RegExp(regexPattern).test(url);
        } else {
          return new RegExp(rule.urlPattern).test(url);
        }
      } catch (e) {
        return false;
      }
    });
  }

  processUrlRewrite(url, rule) {
    if (!rule.rewriteUrl) return url;
    
    try {
      if (rule.urlPattern.includes('*')) {
        // Handle wildcard replacement
        const regexPattern = rule.urlPattern
          .replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
          .replace(/\\\*/g, '(.*)');
        const regex = new RegExp(regexPattern);
        const match = url.match(regex);
        
        if (match) {
          let rewriteUrl = rule.rewriteUrl;
          for (let i = 1; i < match.length; i++) {
            rewriteUrl = rewriteUrl.replace(`$${i}`, match[i]);
          }
          return rewriteUrl;
        }
      } else {
        // Handle regex replacement
        const regex = new RegExp(rule.urlPattern);
        return url.replace(regex, rule.rewriteUrl);
      }
    } catch (error) {
      console.error('URL rewrite error:', error);
    }
    
    return url;
  }

  async saveRule(rule) {
    if (rule.id) {
      // Update existing rule
      const index = this.rules.findIndex(r => r.id === rule.id);
      if (index !== -1) {
        this.rules[index] = rule;
      }
    } else {
      // Add new rule
      rule.id = Date.now().toString();
      this.rules.push(rule);
    }

    await this.saveData();
    await this.updateDeclarativeRules();
  }

  async deleteRule(ruleId) {
    this.rules = this.rules.filter(rule => rule.id !== ruleId);
    await this.saveData();
    await this.updateDeclarativeRules();
  }

  async toggleRule(ruleId) {
    const rule = this.rules.find(r => r.id === ruleId);
    if (rule) {
      rule.enabled = !rule.enabled;
      await this.saveData();
      await this.updateDeclarativeRules();
    }
  }

  addLog(type, url, originalUrl, details = {}) {
    const logEntry = {
      id: Date.now().toString() + Math.random().toString(36).substring(2),
      timestamp: Date.now(),
      type: type,
      url: url,
      originalUrl: originalUrl,
      domain: this.getDomain(originalUrl),
      details: details
    };

    this.logs.push(logEntry);
    
    // Keep only recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
    
    // Save logs periodically (not on every log to avoid performance issues)
    if (this.logs.length % 10 === 0) {
      this.saveData();
    }
  }

  cleanOldLogs() {
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    const oldLength = this.logs.length;
    this.logs = this.logs.filter(log => log.timestamp > oneWeekAgo);
    
    if (this.logs.length !== oldLength) {
      this.saveData();
    }
  }

  getDomain(url) {
    try {
      return new URL(url).hostname;
    } catch (e) {
      return url;
    }
  }

  getHeader(headers, name) {
    const header = headers.find(h => h.name.toLowerCase() === name.toLowerCase());
    return header ? header.value : null;
  }

  headersToObject(headers) {
    const obj = {};
    headers.forEach(header => {
      obj[header.name] = header.value;
    });
    return obj;
  }

  detectCharsetFromHeaders(headers) {
    const contentType = this.getHeader(headers, 'content-type') || '';
    const charsetMatch = contentType.match(/charset=([^;]+)/i);
    return charsetMatch ? charsetMatch[1].toLowerCase() : 'utf-8';
  }

  async setupDeclarativeRules() {
    // Clear existing rules
    try {
      const existingRules = await chrome.declarativeNetRequest.getDynamicRules();
      const ruleIds = existingRules.map(rule => rule.id);
      if (ruleIds.length > 0) {
        await chrome.declarativeNetRequest.updateDynamicRules({
          removeRuleIds: ruleIds
        });
      }
    } catch (error) {
      console.error('Failed to clear existing rules:', error);
    }

    // Add new rules based on current configuration
    await this.updateDeclarativeRules();
  }

  async updateDeclarativeRules() {
    if (!this.isEnabled) return;

    const declarativeRules = [];
    let ruleId = 1;

    for (const rule of this.rules) {
      if (!rule.enabled || !rule.rewriteUrl) continue;

      try {
        // Simple URL pattern matching for declarative net request
        let urlFilter = rule.urlPattern;

        // Convert simple wildcards to declarative net request format
        if (urlFilter.includes('*')) {
          urlFilter = urlFilter.replace(/\*/g, '*');
        }

        const declarativeRule = {
          id: ruleId++,
          priority: 1,
          action: {
            type: 'redirect',
            redirect: {
              url: rule.rewriteUrl
            }
          },
          condition: {
            urlFilter: urlFilter,
            resourceTypes: ['main_frame', 'sub_frame', 'xmlhttprequest']
          }
        };

        declarativeRules.push(declarativeRule);
      } catch (error) {
        console.error('Failed to create declarative rule for:', rule.name, error);
      }
    }

    // Update declarative rules
    if (declarativeRules.length > 0) {
      try {
        await chrome.declarativeNetRequest.updateDynamicRules({
          addRules: declarativeRules
        });
        console.log('Updated declarative rules:', declarativeRules.length);
      } catch (error) {
        console.error('Failed to update declarative rules:', error);
      }
    }
  }
}

// Initialize the request modifier
const requestModifier = new RequestModifier();
