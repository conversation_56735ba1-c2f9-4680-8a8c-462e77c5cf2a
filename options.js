// Options page script for Request Modifier Pro

class OptionsManager {
  constructor() {
    this.rules = [];
    this.settings = {
      enabled: true,
      maxLogEntries: 1000,
      logRetentionDays: 7,
      debugMode: false,
      defaultCharset: 'utf-8',
      autoDetectEncoding: true,
      fixMojibake: true
    };
    
    this.functionTemplates = {
      'encoding-fix': `// Fix encoding issues automatically
if (response.headers['content-type'] && response.headers['content-type'].includes('text/html')) {
  const charset = utils.detectCharset(response.headers, response.body);
  console.log('Detected charset:', charset);
  
  if (charset === 'gbk' || charset === 'gb2312') {
    // Handle GBK encoding
    console.log('Processing GBK encoded content for:', response.url);
    
    // You can add custom logic here to handle the response
    // For example, modify the page content or headers
  }
}`,
      
      'content-replace': `// Replace specific content in the response
if (response.body && response.headers['content-type'].includes('text/html')) {
  // Example: Replace specific text
  const originalContent = response.body;
  const modifiedContent = originalContent.replace(/old-text/g, 'new-text');
  
  if (modifiedContent !== originalContent) {
    console.log('Content replaced for:', response.url);
    // Note: In content script context, you can modify the DOM directly
    // document.body.innerHTML = modifiedContent;
  }
}`,
      
      'header-log': `// Log response headers for analysis
console.log('Response headers for:', response.url);
console.log('Status:', response.status);
console.log('Headers:', response.headers);

// Log specific headers of interest
const contentType = response.headers['content-type'];
const contentLength = response.headers['content-length'];
const server = response.headers['server'];

console.log('Content-Type:', contentType);
console.log('Content-Length:', contentLength);
console.log('Server:', server);`,
      
      'custom-redirect': `// Custom redirect logic based on response
if (response.status === 404) {
  console.log('404 detected for:', response.url);
  
  // Example: Redirect to a custom 404 page
  const customUrl = 'https://example.com/custom-404';
  window.location.href = customUrl;
} else if (response.headers['content-type'].includes('application/json')) {
  // Handle JSON responses
  try {
    const data = JSON.parse(response.body);
    console.log('JSON response data:', data);
    
    // Add custom logic for JSON processing
  } catch (e) {
    console.error('Failed to parse JSON:', e);
  }
}`
    };
    
    this.init();
  }

  async init() {
    await this.loadData();
    this.setupEventListeners();
    this.updateUI();
  }

  async loadData() {
    try {
      // Load settings
      const settingsData = await Utils.getStorageData(['settings']);
      if (settingsData.settings) {
        this.settings = { ...this.settings, ...settingsData.settings };
      }

      // Load rules
      const rulesResponse = await this.sendMessage({ type: 'getRules' });
      this.rules = rulesResponse.rules || [];
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  }

  setupEventListeners() {
    // General settings
    document.getElementById('saveGeneralSettings').addEventListener('click', () => this.saveGeneralSettings());
    document.getElementById('saveEncodingSettings').addEventListener('click', () => this.saveEncodingSettings());

    // Rules management
    document.getElementById('addNewRuleBtn').addEventListener('click', () => this.addNewRule());
    document.getElementById('exportRulesBtn').addEventListener('click', () => this.exportRules());
    document.getElementById('importRulesBtn').addEventListener('click', () => this.importRules());

    // Function templates
    document.getElementById('functionTemplate').addEventListener('change', () => this.loadTemplate());
    document.getElementById('copyTemplateBtn').addEventListener('click', () => this.copyTemplate());
    document.getElementById('testTemplateBtn').addEventListener('click', () => this.testTemplate());

    // Import/Export
    document.getElementById('exportConfigBtn').addEventListener('click', () => this.exportConfig());
    document.getElementById('exportRulesOnlyBtn').addEventListener('click', () => this.exportRulesOnly());
    document.getElementById('importConfigFile').addEventListener('change', () => this.handleImportFile());
    document.getElementById('importConfigBtn').addEventListener('click', () => this.importConfig());
  }

  updateUI() {
    this.updateGeneralSettings();
    this.updateEncodingSettings();
    this.updateRulesList();
    this.updateRulesStats();
  }

  updateGeneralSettings() {
    document.getElementById('enableExtension').checked = this.settings.enabled;
    document.getElementById('maxLogEntries').value = this.settings.maxLogEntries;
    document.getElementById('logRetentionDays').value = this.settings.logRetentionDays;
    document.getElementById('debugMode').checked = this.settings.debugMode;
  }

  updateEncodingSettings() {
    document.getElementById('defaultCharset').value = this.settings.defaultCharset;
    document.getElementById('autoDetectEncoding').checked = this.settings.autoDetectEncoding;
    document.getElementById('fixMojibake').checked = this.settings.fixMojibake;
  }

  updateRulesList() {
    const rulesList = document.getElementById('rulesList');
    
    if (this.rules.length === 0) {
      rulesList.innerHTML = `
        <div class="text-center" style="color: var(--text-secondary); padding: 40px;">
          No rules configured yet
        </div>
      `;
      return;
    }

    rulesList.innerHTML = this.rules.map(rule => this.renderRuleItem(rule)).join('');
  }

  renderRuleItem(rule) {
    return `
      <div class="rule-item" data-rule-id="${rule.id}">
        <div class="rule-header">
          <div class="rule-name">${Utils.escapeHtml(rule.name)}</div>
          <div class="rule-actions">
            <span class="status ${rule.enabled ? 'status-active' : 'status-inactive'}">
              ${rule.enabled ? 'ENABLED' : 'DISABLED'}
            </span>
            <button class="btn btn-small btn-outline" onclick="optionsManager.toggleRule('${rule.id}')">
              ${rule.enabled ? 'Disable' : 'Enable'}
            </button>
            <button class="btn btn-small btn-secondary" onclick="optionsManager.editRule('${rule.id}')">
              Edit
            </button>
            <button class="btn btn-small btn-danger" onclick="optionsManager.deleteRule('${rule.id}')">
              Delete
            </button>
          </div>
        </div>
        <div class="rule-pattern">${Utils.escapeHtml(rule.urlPattern)}</div>
        ${rule.rewriteUrl ? `<div class="rule-pattern" style="color: var(--secondary-color);">→ ${Utils.escapeHtml(rule.rewriteUrl)}</div>` : ''}
        ${rule.customFunction ? '<div class="status status-info" style="margin-top: 8px;">Has Custom Function</div>' : ''}
      </div>
    `;
  }

  updateRulesStats() {
    const totalRules = document.getElementById('totalRules');
    const activeRules = document.getElementById('activeRules');

    totalRules.textContent = `${this.rules.length} total rules`;
    
    const activeCount = this.rules.filter(rule => rule.enabled).length;
    activeRules.textContent = `${activeCount} active`;
  }

  async saveGeneralSettings() {
    this.settings.enabled = document.getElementById('enableExtension').checked;
    this.settings.maxLogEntries = parseInt(document.getElementById('maxLogEntries').value);
    this.settings.logRetentionDays = parseInt(document.getElementById('logRetentionDays').value);
    this.settings.debugMode = document.getElementById('debugMode').checked;

    try {
      await Utils.setStorageData({ settings: this.settings });
      
      // Also update the extension enabled state
      await this.sendMessage({ type: 'toggleEnabled' });
      
      this.showNotification('General settings saved successfully', 'success');
    } catch (error) {
      console.error('Failed to save general settings:', error);
      this.showNotification('Failed to save settings', 'error');
    }
  }

  async saveEncodingSettings() {
    this.settings.defaultCharset = document.getElementById('defaultCharset').value;
    this.settings.autoDetectEncoding = document.getElementById('autoDetectEncoding').checked;
    this.settings.fixMojibake = document.getElementById('fixMojibake').checked;

    try {
      await Utils.setStorageData({ settings: this.settings });
      this.showNotification('Encoding settings saved successfully', 'success');
    } catch (error) {
      console.error('Failed to save encoding settings:', error);
      this.showNotification('Failed to save encoding settings', 'error');
    }
  }

  addNewRule() {
    // Open popup for adding new rule (similar to popup.js)
    chrome.tabs.create({ url: chrome.runtime.getURL('popup.html') });
  }

  async toggleRule(ruleId) {
    try {
      await this.sendMessage({ type: 'toggleRule', ruleId });
      await this.loadData();
      this.updateUI();
    } catch (error) {
      console.error('Failed to toggle rule:', error);
    }
  }

  editRule(ruleId) {
    const rule = this.rules.find(r => r.id === ruleId);
    if (!rule) return;

    // Create edit modal (simplified version)
    const editUrl = `popup.html?edit=${ruleId}`;
    chrome.tabs.create({ url: chrome.runtime.getURL(editUrl) });
  }

  async deleteRule(ruleId) {
    if (confirm('Are you sure you want to delete this rule?')) {
      try {
        await this.sendMessage({ type: 'deleteRule', ruleId });
        await this.loadData();
        this.updateUI();
        this.showNotification('Rule deleted successfully', 'success');
      } catch (error) {
        console.error('Failed to delete rule:', error);
        this.showNotification('Failed to delete rule', 'error');
      }
    }
  }

  exportRules() {
    const dataStr = JSON.stringify(this.rules, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `request-modifier-rules-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  }

  importRules() {
    const input = document.getElementById('hiddenFileInput');
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = async (e) => {
          try {
            const rules = JSON.parse(e.target.result);
            
            // Validate rules format
            if (!Array.isArray(rules)) {
              throw new Error('Invalid rules format');
            }

            // Import rules
            for (const rule of rules) {
              rule.id = Utils.generateId(); // Generate new IDs
              await this.sendMessage({ type: 'saveRule', rule });
            }

            await this.loadData();
            this.updateUI();
            this.showNotification(`Imported ${rules.length} rules successfully`, 'success');
          } catch (error) {
            console.error('Failed to import rules:', error);
            this.showNotification('Failed to import rules: ' + error.message, 'error');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }

  loadTemplate() {
    const templateSelect = document.getElementById('functionTemplate');
    const templateCode = document.getElementById('templateCode');
    const copyBtn = document.getElementById('copyTemplateBtn');
    const testBtn = document.getElementById('testTemplateBtn');

    const selectedTemplate = templateSelect.value;
    
    if (selectedTemplate && this.functionTemplates[selectedTemplate]) {
      templateCode.value = this.functionTemplates[selectedTemplate];
      copyBtn.disabled = false;
      testBtn.disabled = false;
    } else {
      templateCode.value = 'Select a template to view its code';
      copyBtn.disabled = true;
      testBtn.disabled = true;
    }
  }

  copyTemplate() {
    const templateCode = document.getElementById('templateCode');
    templateCode.select();
    document.execCommand('copy');
    this.showNotification('Template copied to clipboard', 'success');
  }

  testTemplate() {
    const templateCode = document.getElementById('templateCode').value;
    const validation = Utils.validateCustomFunction(templateCode);
    
    if (validation.valid) {
      this.showNotification('Template syntax is valid', 'success');
    } else {
      this.showNotification('Template syntax error: ' + validation.error, 'error');
    }
  }

  exportConfig() {
    const config = {
      settings: this.settings,
      rules: this.rules,
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    };

    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `request-modifier-config-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  }

  exportRulesOnly() {
    this.exportRules();
  }

  handleImportFile() {
    const fileInput = document.getElementById('importConfigFile');
    const importBtn = document.getElementById('importConfigBtn');
    
    importBtn.disabled = !fileInput.files.length;
  }

  importConfig() {
    const fileInput = document.getElementById('importConfigFile');
    const file = fileInput.files[0];
    
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const config = JSON.parse(e.target.result);
        
        // Import settings
        if (config.settings) {
          this.settings = { ...this.settings, ...config.settings };
          await Utils.setStorageData({ settings: this.settings });
        }

        // Import rules
        if (config.rules && Array.isArray(config.rules)) {
          for (const rule of config.rules) {
            rule.id = Utils.generateId(); // Generate new IDs
            await this.sendMessage({ type: 'saveRule', rule });
          }
        }

        await this.loadData();
        this.updateUI();
        this.showNotification('Configuration imported successfully', 'success');
      } catch (error) {
        console.error('Failed to import configuration:', error);
        this.showNotification('Failed to import configuration: ' + error.message, 'error');
      }
    };
    reader.readAsText(file);
  }

  showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `status status-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }
}

// Initialize options manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.optionsManager = new OptionsManager();
});
