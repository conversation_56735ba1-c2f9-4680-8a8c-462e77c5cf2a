<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Options - Request Modifier Pro</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="card">
      <div class="card-header">
        <h1 class="card-title">Request Modifier Pro - Settings</h1>
        <div class="card-subtitle">Configure advanced options and manage rules</div>
      </div>
    </div>

    <!-- General Settings -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">General Settings</h2>
      </div>
      <div class="card-body">
        <div class="form-group">
          <div class="flex flex-between align-center">
            <div>
              <label class="form-label">Enable Extension</label>
              <div style="color: var(--text-secondary); font-size: 14px;">
                Master switch to enable/disable all request modifications
              </div>
            </div>
            <label class="toggle">
              <input type="checkbox" id="enableExtension">
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">Maximum Log Entries</label>
          <input type="number" class="form-input" id="maxLogEntries" min="100" max="10000" value="1000">
          <small style="color: var(--text-secondary);">
            Maximum number of log entries to keep (100-10000)
          </small>
        </div>

        <div class="form-group">
          <label class="form-label">Log Retention (Days)</label>
          <input type="number" class="form-input" id="logRetentionDays" min="1" max="365" value="7">
          <small style="color: var(--text-secondary);">
            Automatically delete logs older than this many days
          </small>
        </div>

        <div class="form-group">
          <div class="flex flex-between align-center">
            <div>
              <label class="form-label">Debug Mode</label>
              <div style="color: var(--text-secondary); font-size: 14px;">
                Enable detailed logging for troubleshooting
              </div>
            </div>
            <label class="toggle">
              <input type="checkbox" id="debugMode">
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <button class="btn btn-primary" id="saveGeneralSettings">Save Settings</button>
      </div>
    </div>

    <!-- Rules Management -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Rules Management</h2>
        <div class="card-subtitle">Create and manage URL modification rules</div>
      </div>
      <div class="card-body">
        <div class="flex flex-between align-center mb-2">
          <div>
            <span id="totalRules" class="status status-active">0 total rules</span>
            <span id="activeRules" class="status status-inactive ml-2">0 active</span>
          </div>
          <div class="flex gap-1">
            <button class="btn btn-outline btn-small" id="importRulesBtn">
              <span>📥</span> Import
            </button>
            <button class="btn btn-outline btn-small" id="exportRulesBtn">
              <span>📤</span> Export
            </button>
            <button class="btn btn-primary btn-small" id="addNewRuleBtn">
              <span>➕</span> Add Rule
            </button>
          </div>
        </div>

        <div id="rulesList">
          <div class="text-center" style="color: var(--text-secondary); padding: 40px;">
            No rules configured yet
          </div>
        </div>
      </div>
    </div>

    <!-- Encoding Settings -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Encoding Settings</h2>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label class="form-label">Default Charset</label>
          <select class="form-select" id="defaultCharset">
            <option value="utf-8">UTF-8</option>
            <option value="gbk">GBK</option>
            <option value="gb2312">GB2312</option>
            <option value="iso-8859-1">ISO-8859-1</option>
            <option value="windows-1252">Windows-1252</option>
          </select>
          <small style="color: var(--text-secondary);">
            Default charset to use when detection fails
          </small>
        </div>

        <div class="form-group">
          <div class="flex flex-between align-center">
            <div>
              <label class="form-label">Auto-detect Encoding</label>
              <div style="color: var(--text-secondary); font-size: 14px;">
                Automatically detect and fix encoding issues
              </div>
            </div>
            <label class="toggle">
              <input type="checkbox" id="autoDetectEncoding" checked>
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>

        <div class="form-group">
          <div class="flex flex-between align-center">
            <div>
              <label class="form-label">Fix Mojibake</label>
              <div style="color: var(--text-secondary); font-size: 14px;">
                Automatically fix common encoding corruption
              </div>
            </div>
            <label class="toggle">
              <input type="checkbox" id="fixMojibake" checked>
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <button class="btn btn-primary" id="saveEncodingSettings">Save Encoding Settings</button>
      </div>
    </div>

    <!-- Custom Function Templates -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Custom Function Templates</h2>
        <div class="card-subtitle">Pre-built templates for common tasks</div>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label class="form-label">Template</label>
          <select class="form-select" id="functionTemplate">
            <option value="">Select a template...</option>
            <option value="encoding-fix">Fix Encoding Issues</option>
            <option value="content-replace">Replace Content</option>
            <option value="header-log">Log Response Headers</option>
            <option value="custom-redirect">Custom Redirect Logic</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">Template Code</label>
          <textarea class="form-textarea" id="templateCode" readonly style="min-height: 200px; font-family: 'Consolas', 'Monaco', monospace;">
            Select a template to view its code
          </textarea>
        </div>

        <div class="flex gap-1">
          <button class="btn btn-secondary" id="copyTemplateBtn" disabled>Copy to Clipboard</button>
          <button class="btn btn-outline" id="testTemplateBtn" disabled>Test Template</button>
        </div>
      </div>
    </div>

    <!-- Import/Export -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Backup & Restore</h2>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label class="form-label">Export Configuration</label>
          <div class="flex gap-1">
            <button class="btn btn-outline flex-1" id="exportConfigBtn">
              <span>📤</span> Export All Settings
            </button>
            <button class="btn btn-outline flex-1" id="exportRulesOnlyBtn">
              <span>📋</span> Export Rules Only
            </button>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">Import Configuration</label>
          <input type="file" class="form-input" id="importConfigFile" accept=".json">
          <button class="btn btn-primary mt-1" id="importConfigBtn" disabled>
            <span>📥</span> Import Configuration
          </button>
        </div>
      </div>
    </div>

    <!-- Hidden file input for import -->
    <input type="file" id="hiddenFileInput" style="display: none;" accept=".json">
  </div>

  <script src="utils.js"></script>
  <script src="options.js"></script>
</body>
</html>
