# 更新日志

## v1.0.0 (2024-06-16)

### ✨ 新功能
- **Manifest V3 支持**：完全兼容最新的Chrome扩展API
- **现代化UI设计**：卡片式布局，一致的配色方案
- **智能编码处理**：自动检测GBK、UTF-8等字符编码
- **请求日志系统**：完整的请求历史记录和筛选功能
- **自定义响应函数**：支持JavaScript处理响应内容
- **URL重写功能**：使用declarativeNetRequest API进行URL重定向
- **规则管理**：启用/禁用、导入/导出规则配置
- **实时监控**：非阻塞式请求监控和处理

### 🔧 技术特性
- **Service Worker**：使用现代的后台脚本架构
- **Declarative Net Request**：高性能的网络请求处理
- **Content Scripts**：页面级别的内容处理
- **Storage API**：本地数据存储和同步
- **Scripting API**：动态脚本注入

### 📋 权限说明
- `declarativeNetRequest`：网络请求重写和拦截
- `storage`：保存规则和设置
- `activeTab`：访问当前标签页
- `scripting`：注入自定义脚本
- `webRequest`：监听网络请求
- `*://*/*`：访问所有HTTP/HTTPS网站

### ⚠️ 重要变化
- **移除webRequestBlocking**：Manifest V3不再支持阻塞式请求拦截
- **使用declarativeNetRequest**：URL重写现在通过声明式规则实现
- **非阻塞监控**：请求监控改为非阻塞模式
- **无图标依赖**：扩展可以在没有自定义图标的情况下运行

### 🐛 已知限制
- 复杂的正则表达式可能在declarativeNetRequest中不被支持
- 某些动态URL重写场景可能需要调整实现方式
- 自定义函数在某些安全上下文中可能受限

### 📚 文档
- [README.md](README.md) - 详细功能说明
- [INSTALL.md](INSTALL.md) - 安装和使用指南
- [QUICKSTART.md](QUICKSTART.md) - 快速开始指南

### 🔄 升级说明
这是首个正式版本，无需从旧版本升级。

### 🆘 支持
如遇问题请：
1. 查看常见问题解答
2. 检查浏览器控制台错误
3. 开启调试模式获取详细日志
4. 在GitHub提交Issue

---

**注意**：此版本要求Chrome 88+，推荐使用Chrome 100+以获得最佳兼容性。
